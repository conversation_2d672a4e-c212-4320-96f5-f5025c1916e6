import { getRequest, postRequest, putRequest, deleteRequest } from './axios'

// GET REQUESTS
const getSubcategory = (params) => getRequest('/game', params)
const getProvider = () => getRequest('/game/provider')
const getPlayer = () => getRequest('/user/profile')
const getAllowUser = (params) => getRequest('/user/accessAllowed', params)
const getEmailVerified = (params) => getRequest('/user/verifyEmail', params)
const getIsUserNameExists = (params) => getRequest('/user/sign-up', params)
const getPromoCode = (params) => getRequest('/user/promo-code', params)
const getPackagesTypesListing = (params) => getRequest('/payment/packages', params)
const getStateListing = (params) => getRequest('/user/state', params)
const getCityListing = (params) => getRequest('/user/city', params)
const getWelcomBonus = () => getRequest('/user/welcome-bonus')
const getDailyBonus = () => getRequest('/user/daily-bonus')
const getPromotionBonus = () => getRequest('/user/promotion-bonus')
const getFreeSpin = (params) => getRequest('/rewards/get-free-spin',params)
const getReferralBonus = () => getRequest('/user/referral-bonus') // eslint-disable-line
const getTierBonus = () => getRequest('/user/get-tier-bonus ')
// const getGamblingHistory = (params) => getRequest('/user/gambling-history', params)
const getSingleGambling = (params) => getRequest('/user/get-gambling', params)
const accessAllowed = () => getRequest('user/accessAllowed')
const getBannersListing = () => getRequest('user/banners')
const getPopupListing = () => getRequest('user/popups')
const getGamesList = (params) => getRequest('/user/casino-games', params)
const getRecentGamesList = (params) => getRequest('/game/recently-played', params)
const initKYC = (params) => getRequest('user/init-kyc', params)
const getTransactions = (params) => getRequest('/payment/transactions', params)
const getGiveawayHisory = (params) => getRequest('rewards/userRaffleTicket', params)
const getPersonalBonusList = (params) => getRequest('/user/personal-bonus', params)
const getBetTransactions = (params) => getRequest('/user/bets', params)
const getBankDetails = (params) => getRequest('/payment/bank-details', params)
const getPostalApi = () => getRequest('/user/postal-code')
const getFavorites = (params) => getRequest('/game/favorite', params)
const getCms = () => getRequest('/user/cms')
const getCmsContent = (params) => getRequest('/user/cms', params)
const getCmsPage = (param) => getRequest('/user/cms-detail', param)
const getVipTiers = () => getRequest('rewards/tiers')
const getLiveWinner = () => getRequest('/game/live-winners')
const getResponsibleGambling = () => getRequest('/user/gambling-history')
const getPaymentStatus = (params) => getRequest('/payment/status', params)
const getUserBonuses = () => getRequest('/user/get-all-bonus')
const getReferAFriendDetail = () => getRequest('user/referral')
const getTournament = (params) => getRequest(`/tournament`, params)
const getTournamentDetail = (params) => getRequest(`/tournament/${params}`)
const getRafflePromotion = () => getRequest('rewards/raffle-promotion')
const getRaffleDetail = () => getRequest('rewards/raffle-detail')
const getCheckSession = () => getRequest('/user/check-session')
const getCheckPromocode = (data) => getRequest('/user/promocode', data)
const getVaultDetails = () => getRequest('/payment/vault-detail')
const generateOtp2FA = () => getRequest('/user/generate-otp-2fa')
const getReferredDetail = (params) => getRequest('user/referred-details', params)
const getHallOfFame = (params) => getRequest('user/hallOfFameRecord', params)
const getSiteLogo = () => getRequest('/common/logo')
const getPayByBank = () => getRequest('/payment/redeem/pay-by-bank')
const getRedeemTrustly = () => getRequest('/payment/redeem/trustly')
const getDynamicBlogPages = (params) => getRequest('/pages/blog-post', params)
const getVipQuetsions = () => getRequest('/user/questionnaire')
const getVipUserAnswers = () => getRequest('/user/user-questionnaire-answer')
const getDynamoPopupData = () => getRequest('/user/getDynamoPopup ')
const getPromotionThumbnail = () => getRequest('/user/promotionThumbnails')
const getDynamicGamePages = (params) => getRequest('pages/game-page', params)
// geocomply
const getLicenseString = (params) => getRequest('/common/get-license-string', params)
const decryptGeoComplyResponse = (params) => getRequest('/common/decrypt-geocomply-data', params)
const getAllowedStates = () => getRequest('/user/get-states')

// maintenance mode
const getMaintenanceModeData = () => getRequest('/common/maintenance-mode')

// POST REQUESTS
const userLogin = (data) => postRequest('/user/login', data)
const createAffiliateUser = (data) => postRequest('/user/apply-affiliate', data)
const addBankAccount = () => postRequest('/payment/redeem/pay-by-bank')
const addPaymentErrors = (data) => postRequest('/payment/deposit/error', data)
// const claimReferralBonus = (data) => postRequest('/user/claim-referral-bonus',data);
// const getOtpCall = (data) => postRequest('/user/phoneVerify', data)
const getOtpCall = (data) => postRequest('/user/phone', data)
// const resetPhoneOtp = (params) => postRequest('/user/resendVerifyOtp',params)
const resetPhoneOtp = (params) => postRequest('/user/phone', params)
// const phoneVerifyCall = (data) => postRequest('/user/verifyPhone', data)
const phoneVerifyCall = (data) => postRequest('/user/phoneVerify', data)
const userLogout = () => postRequest('user/logout')
const userGoogleLogin = (data) => postRequest('/user/googleLogin', data)
const userFaceBookLogin = (data) => postRequest('/user/facebookLogin', data)
const userAppleLogin = (data) => postRequest('/user/appleLogin', data)
const userSignUp = (data) => postRequest('/user/sign-up', data)
const emailOTPVerified = (data) => postRequest('/user/verifyEmail', data)
const phoneForgetVerifyCall = (data) => postRequest('/user/verifyForgetPassword', data)
const resendVerificationEmail = (data) => postRequest('/user/resendVerifyEmail', data)
const forgetPassword = (data) => postRequest('/user/forgetPassword', data)
const verifyForgetPassword = (data) => postRequest('/user/verifyForgetPassword', data)
const updateResponsibleGame = (data) => postRequest('/user/responsible-gambling-setting', data)
const claimWelcomBonus = (data) => postRequest('/user/welcome-bonus', data)
const claimFreeSpinBonus = (data) => postRequest('/rewards/claim-free-spin', data)
// const claimReferralBonus = (data) => postRequest('/user/referred-bonus',data)
const claimReferralBonus = (data) => postRequest('/user/claim-referral-bonus', data)
const claimDailyBonus = (data) => postRequest('/user/daily-bonus', data)
const personalBonus = (data) => postRequest('/user/personal-bonus', data)
const cliamPersonalBonus = (data) => postRequest('/user/claim-personal-bonus', data)
const paysafePay = (data) => getRequest(`/payment/deposit/${data.transactionId}`, data)
const cancelRedemption = (data) => deleteRequest('/payment/redeem', data)
const getCancelRedemption = (data) => getRequest('/payment/redeem', data)
const confirmRedemption = (data) => putRequest('/payment/redeem', data)
const processPayment = (data) => postRequest('/payment/shift4/sale', data)
const getGameLink = (data) => postRequest('/game/launch-game', data)
const processWithdraw = (data) => postRequest('/payment/redeem', data)
const toogleFav = (data) => postRequest('/game/favorite', data)
const createPostalCode = (data) => postRequest('/user/postal-code', data)
const updateSsn = (data) => postRequest('/user/update-ssn', data)
const joinTournament = (data) => postRequest('/tournament', data)
const claimPromotionBonus = (data) => postRequest('/user/promotion-bonus', data)
const uploadProfileImage = (data) => putRequest('/user/profile', data, { 'Content-Type': 'multipart/formdata' })
const showCmsChanges = (data) => putRequest('/user/approveTermAndCondition', data)
const depositVaultCoins = (data) => postRequest('/payment/vault-deposit', data)
const withdrawVaultCoins = (data) => postRequest('/payment/vault-withdraw', data)
const verifyOtp2FA = (data) => postRequest('/user/verify-otp-2fa', data)
const disabled2FA = (data) => postRequest('/user/disable-auth', data)
const referralBonusClaim = (data) => postRequest('/user/claim-referral-bonus', data) // eslint-disable-line
const getDemoGameLaunch = (data) => getRequest('/game/demo', data)
const getPostalCode = () => postRequest('/user/postal-code')
const postLowGCBonus = (data) => postRequest('/user/claim-gc-bonus', data)
const claimTierBonus = (data) => postRequest('/rewards/claim-tier', data)
const contactUsData = (data) => postRequest('/common/contact-us', data)
const addVipUserAnswers = (data) => postRequest('/user/user-questionnaire-answer', data)
const dynamoKeyData = (data) => postRequest('/user/dynamo',data)

// PUT REQUESTS
const changePassword = (data) => putRequest('/user/changePassword', data)
const updateProfile = (data) => postRequest('/user/profile', data)
const updateBankData = (data) => putRequest('/payment/bank-details', data, { 'Content-Type': 'multipart/formdata' })
const removeResponsibleGame = (data) => putRequest('/user/remove-responsible-setting', data)
const addUserName = (data) => putRequest('/user/sign-up', data)
const marketingSetting = (data) => putRequest('/user/setting-marketing', data)
const updateVipUserAnswers = (data) => putRequest('/user/user-questionnaire-answer', data)

// DELETE REQUESTS
const deleteBankAccount = (data) => deleteRequest('/payment/redeem/pay-by-bank', data)
const deletePaySafe = () => deleteRequest('/payment/deposit/lock')

// Payment New flow
const initPay = (data) => postRequest('/payment/deposit', data)
const deleteDeposit = (data) => deleteRequest('/payment/deposit', data)
const skrillPayment = (data) => postRequest('/payment/deposit/skrill', data)
const pbbPayment = (data) => postRequest('/payment/deposit/pay-by-bank', data)
const savedPbbPayment = (data) => postRequest('/payment/deposit/pay-by-bank/saved-account', data)
const deleteSavedBank = (data) => deleteRequest('/payment/deposit/pay-by-bank/saved-account', data)
const deleteSavedCard = (data) => deleteRequest('/payment/deposit/remove-save-card', data)
const getApplicablePromocode = (params) => getRequest('/payment/promocode', params)
const applyPackagePromoCode = (data) => postRequest('/payment/promocode', data)
const deletePromocode = (data) => deleteRequest('/payment/promocode', data)
const paymentProcess = (data) => postRequest('/payment/deposit/process-deposit', data)
const savedCardInitDepsit = (data) => postRequest('/payment/deposit/save-card', data)

const trustlyDeposit = (data) => postRequest('/payment/deposit/trustly', data)
const initializeTrustlyPayment = (data) => postRequest('/payment/deposit/initialize-trustly', data)
const savedTrustlyPayment = (data) => postRequest('/payment/deposit/trustly/saved-account', data)
const trustlyRedeem = () => postRequest('/payment/redeem/trustly')
const getUserPreferredPayments = () => getRequest('/user/preferred-payment')
const deleteUserPreferredPayments = (data) => deleteRequest('/user/preferred-payment', data)

// Jackpot

const getJackpotData = (params) => getRequest('/game/jackpot', params)
const optJackpot = (data) => putRequest('/game/jackpot', data)

// scratch card

const claimScratchCard = (data) => postRequest('/user/claimScratchCardBonus', data)

// Subscription
const getSubscription = (params) => getRequest('/subscription', params)
const getSubscriptionsData = (params) => getRequest('/subscription/plans', params)
const initiateSubscription = (data) => postRequest('/payment/subscription', data)
const processSubscriptionPayment = (data) => postRequest('/payment/deposit/process-subscription-deposit', data)
const upgradeSubscription = (params) => getRequest('/subscription/upgrade-subscription', params)
const cancelSubscription = (params) => deleteRequest('/subscription/cancel-subscription', params)
const getPlayerSubscriptionData = (params) => getRequest('/subscription/player', params)

export {
  claimFreeSpinBonus,
  getFreeSpin,
  getAllowUser,
  userAppleLogin,
  getSubcategory,
  getProvider,
  userLogin,
  userLogout,
  getOtpCall,
  userSignUp,
  phoneVerifyCall,
  resetPhoneOtp,
  getPlayer,
  userGoogleLogin,
  getIsUserNameExists,
  getPromoCode,
  applyPackagePromoCode,
  resendVerificationEmail,
  forgetPassword,
  getEmailVerified,
  verifyForgetPassword,
  getPackagesTypesListing,
  getStateListing,
  getCityListing,
  userFaceBookLogin,
  updateResponsibleGame,
  getWelcomBonus,
  getDailyBonus,
  claimWelcomBonus,
  claimDailyBonus,
  showCmsChanges,
  // getGamblingHistory,
  getSingleGambling,
  changePassword,
  accessAllowed,
  getBannersListing,
  getGamesList,
  initPay,
  processPayment,
  updateProfile,
  getGameLink,
  initKYC,
  processWithdraw,
  getTransactions,
  getBetTransactions,
  toogleFav,
  updateBankData,
  getBankDetails,
  getPostalApi,
  createPostalCode,
  getFavorites,
  getCms,
  getCmsPage,
  removeResponsibleGame,
  getVipTiers,
  getLiveWinner,
  getRecentGamesList,
  getResponsibleGambling,
  getPaymentStatus,
  updateSsn,
  uploadProfileImage,
  claimReferralBonus,
  getPopupListing,
  getUserBonuses,
  phoneForgetVerifyCall,
  emailOTPVerified,
  addUserName,
  paysafePay,
  createAffiliateUser,
  personalBonus,
  getPersonalBonusList,
  cliamPersonalBonus,
  getReferAFriendDetail,
  cancelRedemption,
  confirmRedemption,
  getTournament,
  getTournamentDetail,
  joinTournament,
  getRafflePromotion,
  getRaffleDetail,
  getCheckSession,
  getPromotionBonus,
  claimPromotionBonus,
  getCancelRedemption,
  getCheckPromocode,
  getGiveawayHisory,
  getVaultDetails,
  generateOtp2FA,
  depositVaultCoins,
  withdrawVaultCoins,
  verifyOtp2FA,
  marketingSetting,
  disabled2FA,
  getReferredDetail,
  getHallOfFame,
  getCmsContent,
  getDemoGameLaunch,
  getPayByBank,
  getRedeemTrustly,
  addBankAccount,
  deleteBankAccount,
  getPostalCode,
  getLicenseString,
  decryptGeoComplyResponse,
  getAllowedStates,
  postLowGCBonus,
  getTierBonus,
  claimTierBonus,
  addPaymentErrors,
  deletePaySafe,
  deletePromocode,
  getSiteLogo,
  contactUsData,
  deleteDeposit,
  skrillPayment,
  pbbPayment,
  savedPbbPayment,
  deleteSavedBank,
  getApplicablePromocode,
  paymentProcess,
  deleteSavedCard,
  savedCardInitDepsit,
  getMaintenanceModeData,
  trustlyDeposit,
  initializeTrustlyPayment,
  savedTrustlyPayment,
  trustlyRedeem,
  getDynamicBlogPages,
  getVipQuetsions,
  getVipUserAnswers,
  updateVipUserAnswers,
  addVipUserAnswers,
  dynamoKeyData,
  getDynamoPopupData,
  getUserPreferredPayments,
  deleteUserPreferredPayments,
  getJackpotData,
  optJackpot,
  getPromotionThumbnail,
  claimScratchCard,
  getDynamicGamePages,
  getSubscription,
  getSubscriptionsData,
  initiateSubscription,
  processSubscriptionPayment,
  upgradeSubscription,
  cancelSubscription,
  getPlayerSubscriptionData
}
