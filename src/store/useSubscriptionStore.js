import { create } from 'zustand'

const initialState = {
  userSubscription: null,
  subscriptionsData: [],
  subscriptionId: null,
  selectedPlan: null,
  planType: 'MONTHLY',
  refundAmount: 0,
  remainingDays: 0,
  // Payment
  tokens: {
    singleUseCustomerToken: '',
    paymentHandleToken: '',
    transactionId: ''
  },
  data: {
    isPaymentScreenLoading: false,
    depositInitiated: false,
    depositInitFailed: false,
    packageData: null,
    finalAmount: null,
    discountAmount: null,
    paymentData: null,
    demoInstance: null,
    activePaymentMethods: [],
    selectedPaymentMethod: null,
    preferredPayments: null,
    selectedPreferredPayment: null,
    selectedPreferredPaymentId: ''
  },
  status: {
    value: 'payment',
    isLoading: false,
    confettiPopper: null,
    isPaymentScreenLoading: false,
    depositInitiated: false,
    depositInitFailed: {},
    paymentRedirection: false,
    paymentDisabled: true,
    showAllPayments: false
  },
  card: {
    cardHolderName: '',
    savePaymentToggle: true,
    selectedSavedCard: '',
    cardErrors: {
      cardHolderName: '',
      cardNumber: '',
      expiryDate: '',
      cvv: ''
    },
    cardBrandRecognition: ''
  }
}

export const useSubscriptionStore = create((set, get) => ({
  ...initialState,

  // Setters
  setUserSubscription: (data) => set({ userSubscription: data }),
  setSubscriptionsData: (data) => set({ subscriptionsData: data }),
  setSelectedPlan: (data) => set({ selectedPlan: data }),
  setSubscriptionId: (id) => set({ subscriptionId: id }),
  setPlanType: (planType) => set({ planType: planType }),
  setRefundAmount: (amount) => set({ refundAmount: amount }),
  setRemainingDays: (days) => set({ remainingDays: days }),

  // Payment Setters
  setPaymentToken: (key, value) =>
    set((state) => ({
      tokens: {
        ...state.tokens,
        [key]: value
      }
    })),

  setPaymentData: (key, value) =>
    set((state) => ({
      data: {
        ...state.data,
        [key]: value
      }
    })),

  setPaymentStatus: (key, value) =>
    set((state) => ({
      status: {
        ...state.status,
        [key]: value
      }
    })),

  setPaymentCard: (key, value) =>
    set((state) => ({
      card: {
        ...state.card,
        [key]: value
      }
    })),

  setCardErrors: (field, error) =>
    set((state) => ({
      card: {
        ...state.card,
        cardErrors: {
          ...state.card.cardErrors,
          [field]: error
        }
      }
    })),

  // Reset

  resetSubscriptionStore: () =>
    set((state) => ({
      ...JSON.parse(JSON.stringify(initialState)),
      userSubscription: state.userSubscription
    }))

}))
