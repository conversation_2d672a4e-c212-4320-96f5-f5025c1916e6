export const formatFeatureValue = (name, value) => {
  switch (name) {
    case 'Daily Bonus Multiplier':
      return `${value}X`
    case 'Tournament Joining Fee Discount':
    case 'Vault Interest Rate':
    case 'Exclusive Package Discount':
      return `${value}%`
    case 'Weekly Free Spin':
      return value
    case 'Subscriber Only Tournament':
      return null
    case 'Guaranteed Redemption Approved Time for Subscribers':
      return `${value} days`
    default:
      return value
  }
}
