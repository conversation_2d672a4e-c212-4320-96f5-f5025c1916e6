import React, { useEffect, useState } from 'react'
import {
  Box,
  Grid,
  Typography,
  Switch,
} from '@mui/material'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'

import subscriptionQuery from '../../reactQuery/subscriptionQuery'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
import { usePortalStore } from '../../store/store'
import PaymentStatus from '../../components/PaymentStatus'
import StepperForm from '../../components/StepperForm'
import { useLocation } from 'react-router-dom'
import useStyles from './Subscription.styles'
import StarIcon from '@mui/icons-material/Star'
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents'
import useGetDeviceType from '../../utils/useGetDeviceType'
import SubscriptionFaq from './components/SubscriptionFaq'
import SubscriptionCard from './components/SubscriptionCard'

const Subscriptions = () => {
  const { isMobile } = useGetDeviceType()
  const classes = useStyles()
  const location = useLocation()
  const portalStore = usePortalStore(state => state)

  const {
    userSubscription,
    setUserSubscription,
    selectedPlan,
    setSelectedPlan,
    setPlanType,
    setRefundAmount,
    setRemainingDays,
    setPaymentData
  } = useSubscriptionStore(state => state)

  const [yearly, setYearly] = useState(false)
  const [subscriptionsData, setSubscriptionsData] = useState([])
  const [currentPlan, setCurrentPlan] = useState(null)
  const [isUpgrade, setIsUpgrade] = useState(false)
  const [acceptedTerms, setAcceptedTerms] = useState(false)

  const { mutate: getSubscriptionStatus } =
    subscriptionQuery.getSubscriptionMutation({
      onSuccess: res => {
        setUserSubscription(res?.data?.data)
        refetchSubscriptions()
      },
      onError: error => console.log('Error getting profile:', error)
    })

  const { data, refetch: refetchSubscriptions } =
    subscriptionQuery.getSubscriptionDataQuery({
      successToggler: res => {
        if (res.currentSubscriptionPlan) {
          setSubscriptionsData([...res.data, res.currentSubscriptionPlan])
        } else {
          setSubscriptionsData(res.data)
        }
        setCurrentPlan(res?.currentSubscriptionPlan || null)
        setIsUpgrade(res?.upgradeSubscription || false)
        if (res?.data?.length) setSelectedPlan(res.data[0])
      }
    })

  const handleBuyNow = (plan) => {
    setSelectedPlan(plan)
    if (!plan) return
    portalStore.openPortal(
      () => (
        <Box className='stepper-outer-box'>
          <StepperForm
            stepperCalledFor='subscription_purchase'
            packageDetails={plan}
          />
        </Box>
      ),
      'StepperModal'
    )
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const statuss = params.get('status')
    if (statuss === 'success' || statuss === 'failed' || statuss === 'cancelled') {
      const data = {
        transactionId: params.get('transactionId'),
        status: statuss,
        paymentMethod: params.get('paymentMethod'),
        scCoin: params.get('scCoin'),
        gcCoin: params.get('gcCoin'),
        bonusSc: params.get('bonusSc'),
        bonusGc: params.get('bonusGc'),
        amount: params.get('amount')
      }
      portalStore.openPortal(() => <PaymentStatus paymentDetails={data} />, 'paymentmodal')
      getSubscriptionStatus()
    }
    refetchSubscriptions()
  }, [location])

  // Demo Testimonials
  const testimonials = [
    { name: 'Alex J.', review: 'GameVault Pro changed how I play! 🎮🔥', rating: 5 },
    { name: 'Sarah C.', review: 'Love the Premium plan—worth every rupee!', rating: 5 },
    { name: 'Mike R.', review: 'Super smooth payments & instant access.', rating: 4 }
  ]

  // Demo Live Winners
  const liveWinners = [
    { name: 'John D.', amount: '₹2,500', game: 'Cyber Quest' },
    { name: 'Sarah M.', amount: '₹1,800', game: 'Space Warriors' },
    { name: 'Alex K.', amount: '₹3,200', game: 'Dragon Realm' },
    { name: 'Emma L.', amount: '₹950', game: 'Neon Racing' }
  ]

  return (
    <div className={classes.lobbyRight}>
      {/* Hero Section */}
      <div className={classes.headerWrapper}>
        <div className={classes.headerOverlay} />
        <div className={classes.headerContent}>
          <h1 className={classes.headerTitle}>
            Choose Your
            <span className={classes.headerAccent}>Gaming Destiny</span>
          </h1>
          <div className={classes.promoBadge}>
            🔥 LIMITED TIME: Save 25% on Annual Plans!
          </div>
        </div>
      </div>

      {/* Toggle Monthly / Yearly */}
      <div className={classes.toggleContainer}>
        <Box>
          <span className={!yearly ? classes.toggleLabelActive : ''}>Monthly</span>
          <Switch
            checked={yearly}
            onChange={() => {
              setPlanType(!yearly ? 'MONTHLY' : 'YEARLY')
              setYearly(!yearly)
            }}
            color='warning'
          />
          <span className={yearly ? classes.toggleLabelActive : ''}>Yearly</span>
        </Box>
      </div>

      {!isMobile
        ? (
          <div className={classes.desktopPlans}>
            {subscriptionsData.map(plan => {
              const monthlyPrice = plan.monthlyAmount
              const yearlyPrice = (plan.yearlyAmount).toFixed()
              const dailyPrice = yearly ? (yearlyPrice / 30).toFixed(2) : (monthlyPrice / 30).toFixed(2)
              const priceToShow = yearly ? yearlyPrice : monthlyPrice

              return (
                <SubscriptionCard
                  key={plan.subscriptionId}
                  plan={plan}
                  priceToShow={priceToShow}
                  monthlyPrice={monthlyPrice}
                  yearlyPrice={yearlyPrice}
                  dailyPrice={dailyPrice}
                  yearly={yearly}
                  handleBuyNow={handleBuyNow}
                />
              )
            })}
          </div>)
        : (
          <div className={classes.swiperContainer}>
            <Swiper
              slidesPerView={1.1}
              spaceBetween={16}
              centeredSlides
              pagination={{ clickable: true }}
              autoplay={{ delay: 4000 }}
              modules={[Autoplay, Pagination]}
            >
              {subscriptionsData.map(plan => {
                const monthlyPrice = plan.monthlyAmount
                const yearlyPrice = (plan.yearlyAmount).toFixed()
                const dailyPrice = yearly ? (yearlyPrice / 30).toFixed(2) : (monthlyPrice / 30).toFixed(2)
                const priceToShow = yearly ? yearlyPrice : monthlyPrice

                return (
                  <SwiperSlide key={plan.subscriptionId}>
                    <SubscriptionCard
                      key={plan.subscriptionId}
                      plan={plan}
                      priceToShow={priceToShow}
                      monthlyPrice={monthlyPrice}
                      yearlyPrice={yearlyPrice}
                      dailyPrice={dailyPrice}
                      yearly={yearly}
                      handleBuyNow={handleBuyNow}
                    />
                  </SwiperSlide>
                )
              })}
            </Swiper>
          </div>)}
      {/* SOCIAL PROOF */}
      <Box className={classes.socialProofSection}>
        <Typography variant='h4' gutterBottom>⭐ What Our Players Say</Typography>
        <Grid container spacing={3} justifyContent='center'>
          {testimonials.map((t, idx) => (
            <Grid item xs={12} md={4} key={idx}>
              <div className={classes.testimonialCard}>
                {[...Array(t.rating)].map((_, i) => (
                  <StarIcon key={i} className={classes.star} />
                ))}
                <Typography variant='body1' style={{ margin: '10px 0' }}>{t.review}</Typography>
                <Typography variant='subtitle2' style={{ color: '#FDB72E' }}>- {t.name}</Typography>
              </div>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* LIVE WINNERS */}
      <Box className={classes.liveWinners}>
        <Typography variant='h6' textAlign='center' gutterBottom>🏆 Live Winners</Typography>
        <Box className={classes.winnerTicker}>
          {[...liveWinners, ...liveWinners].map((winner, idx) => (
            <div className={classes.winnerItem} key={idx}>
              <EmojiEventsIcon className={classes.winnerIcon} />
              {winner.name} won {winner.amount} in {winner.game}
            </div>
          ))}
        </Box>
      </Box>

      {/* FAQ */}
      <Box className={classes.faqSection}>
        <SubscriptionFaq />
      </Box>
    </div>
  )
}

export default Subscriptions
