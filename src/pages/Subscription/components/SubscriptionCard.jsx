import React from 'react'
import { But<PERSON>, Divider } from '@mui/material'
import useStyles from '../Subscription.styles'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import { formatFeatureValue } from '../helpers'

const SubscriptionCard = ({ plan, priceToShow, monthlyPrice, yearlyPrice, dailyPrice, yearly, handleBuyNow }) => {
  const classes = useStyles()

  return (
    <div key={plan.subscriptionId} className={classes.card}>
      For casual players who want steady value
      <h2 className={classes.planName}>{plan.name}</h2>
      <h1 className={classes.price}>
        ${priceToShow}{' '}
        <span className={classes.priceNote}>/ month</span>
      </h1>

      <p className={classes.coins}>
        Only ${dailyPrice}/ day
        <span className={classes.duration}> (less than a soda)</span>
      </p>
      {yearly && (
        <p className={classes.coins}>
          <span className={classes.duration}>${monthlyPrice}/ month </span>
          Save ${monthlyPrice - yearlyPrice}
        </p>
      )}
      <p className={classes.duration}>{plan.durationDays} days subscription</p>

      <Divider style={{ margin: '12px 0', background: '#333' }} />

      <ul className={classes.featureList}>
        <li>
          <CheckCircleIcon style={{ color: '#FDB72E', fontSize: 16, marginRight: 5 }} />
          {plan.gcCoin.toLocaleString()} Gold Coins + {plan.scCoin} Sweepstakes Coins
        </li>
        {plan.features.map((feature, idx) => (
          <li key={idx} className={classes.featureItem}>
            <CheckCircleIcon style={{ color: '#FDB72E', fontSize: 16, marginRight: 5 }} />
            <span className={classes.featureValue}>{formatFeatureValue(feature.featureDetail.name, feature.featureValue)} </span>
            {feature.featureDetail.name}
          </li>
        ))}
      </ul>

      <Button
        className='btn btn-primary'
        onClick={() => handleBuyNow(plan)}
      >
        START {plan.name.toUpperCase()}
      </Button>

      <p className={classes.footerText}>✅ Cancel anytime • 🔒 Secure payment</p>
    </div>
  )
}

export default SubscriptionCard
