import React from 'react'
import { Accordion, AccordionSummary, AccordionDetails, Typography, Box } from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import { formatPriceWithCommas } from '../../../utils/helpers'

const PurchaseSummaryAccordion = ({ packageDetails, finalAmount, refundAmount }) => {
  return (
    <Accordion className='payment-accordion'>
      <AccordionSummary
        className='payment-accordion-summary'
        expandIcon={<ArrowDropDownIcon sx={{ color: 'white !important' }} />}
        aria-controls='panel1-content'
        id='panel1-header'
      >
        <Typography className='payment-accordion-summary-title' component='p'>
          Purchase Summary
        </Typography>
      </AccordionSummary>
      <AccordionDetails className='payment-accordion-details'>
        <Box className='payment-accordion-detail-item'>
          <Typography className='payment-accordion-detail-item-title' component='p'>Premium 1 month plan price</Typography>
          <Box className='payment-accordion-detail-item-content'>
            <Typography>
              {formatPriceWithCommas(packageDetails?.amount?.toFixed(2))}
            </Typography>
          </Box>
        </Box>
        {refundAmount > 0 && (
          <>
            <Box className='payment-accordion-detail-item'>
              <Typography className='payment-accordion-detail-item-title' component='p'>Remaining amount in your current plan</Typography>
              <Typography className='order-total discount payment-accordion-detail-item-content'>
                - {formatPriceWithCommas((refundAmount).toFixed(2)) || 0.0}
              </Typography>
            </Box>
          </>
        )}
        <Box className='payment-accordion-detail-item total-amount'>
          <Typography sx={{ color: 'white !important', fontWeight: '600' }} component='p'>Total Amount</Typography>
          <Typography>
            <b>${formatPriceWithCommas(finalAmount?.toFixed(2))}</b>
          </Typography>
        </Box>
      </AccordionDetails>
    </Accordion>
  )
}

export default PurchaseSummaryAccordion
