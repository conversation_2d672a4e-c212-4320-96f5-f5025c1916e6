import { Accordion, AccordionDetails, AccordionSummary, Typography } from '@mui/material'
import { useState } from 'react'
import useStyles from '../../LandingPage/LobbyFaq/styles'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'

const SubscriptionFaq = () => {
  const [expanded, setExpanded] = useState('')
  const classes = useStyles()

  // Combined FAQ Data
  const faqItems = [
    {
      id: 'panel1',
      question: '1. Can I cancel anytime?',
      answer: '✅ Yes, you can cancel anytime without fees.'
    },
    {
      id: 'panel2',
      question: '2. Is payment secure?',
      answer: '🔒 Yes. All transactions are encrypted.'
    },
    {
      id: 'panel3',
      question: '3. Do you offer a trial?',
      answer: '🎁 Yes! Enjoy a 7-day free trial on selected plans.'
    }
  ]

  const handleChange = (panelId) => (_, isExpanded) => {
    setExpanded(isExpanded ? panelId : false)
  }

  return (
    <section className={classes.LobbyFaq}>
      <h2 className={classes.H2Text}>
        <Typography variant='span'> Frequently</Typography> Asked Questions
      </h2>
      <section>
        {faqItems.map(({ id, question, answer }) => (
          <Accordion key={id} expanded={expanded === id} onChange={handleChange(id)}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant='h2'>{question}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography>{answer}</Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </section>
    </section>
  )
}

export default SubscriptionFaq
