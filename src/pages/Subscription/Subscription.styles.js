import { makeStyles } from '@mui/styles'

import { LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    background: '#000000 !important'
  },

  /* HERO HEADER */
  headerWrapper: {
    position: 'relative',
    overflow: 'hidden',
    textAlign: 'center',
    padding: '4rem 1rem 6rem',
    background: 'linear-gradient(135deg, #1B181E, #0F0D11)'
  },
  headerOverlay: {
    position: 'absolute',
    inset: 0,
    background: 'rgba(0,0,0,0.4)'
  },
  headerContent: {
    position: 'relative',
    zIndex: 2
  },
  headerTitle: {
    fontSize: '3rem',
    fontWeight: '700',
    margin: 0,
    color: '#fff',
    lineHeight: 1.2,
    [theme.breakpoints.down('md')]: {
      fontSize: '2rem'
    }
  },
  headerAccent: {
    display: 'block',
    color: '#FDB72E'
  },
  promoBadge: {
    marginTop: '1rem',
    background: '#FDB72E',
    color: '#000',
    display: 'inline-block',
    padding: '0.5rem 1.5rem',
    fontWeight: 700,
    borderRadius: '50px',
    fontSize: '1rem',
    animation: '$pulse 1.5s infinite ease-in-out'
  },

  /* TOGGLE */
  toggleContainer: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '0.5rem',
    margin: '2rem 0',
    fontSize: '1.2rem',
    color: '#ccc'
  },
  toggleLabelActive: {
    color: '#FDB72E',
    fontWeight: 'bold'
  },

  /* PLAN CARDS */
  desktopPlans: {
    display: 'flex',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: '2rem',
    padding: '2rem'
  },
  card: {
    width: 320,
    background: '#151515',
    border: '2px solid #FDB72E',
    borderRadius: 12,
    padding: '1.5rem',
    position: 'relative',
    boxShadow: '0 0 15px rgba(0,0,0,0.4)',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    '&:hover': {
      transform: 'translateY(-10px)',
      boxShadow: '0 0 25px rgba(253,183,46,0.6)'
    }
  },
  planName: {
    textAlign: 'center',
    color: '#FDB72E',
    fontSize: '1.5rem',
    marginBottom: '0.3rem'
  },
  price: {
    textAlign: 'center',
    color: '#fff',
    fontSize: '2.2rem',
    margin: 0
  },
  priceNote: {
    fontSize: '1rem',
    color: '#ccc'
  },
  coins: {
    textAlign: 'center',
    color: '#FDB72E',
    fontWeight: 'bold',
    margin: '5px 0'
  },
  duration: {
    textAlign: 'center',
    color: '#999',
    fontSize: '0.9rem'
  },

  /* FEATURES */
  featureList: {
    listStyle: 'none',
    padding: 0,
    marginTop: '1rem',
    width: '100%'
  },
  featureItem: {
    color: '#ddd',
    marginBottom: '0.6rem',
    display: 'flex',
    alignItems: 'center'
  },
  featureValue: {
    marginLeft: 5,
    marginRight: 5,
    color: '#FFD700'
  },

  /* CTA */
  ctaButton: {
    marginTop: '1rem',
    width: '100%',
    padding: '0.8rem',
    background: '#FDB72E',
    color: '#000',
    fontWeight: 'bold',
    fontSize: '1rem',
    border: 'none',
    borderRadius: 8,
    cursor: 'pointer',
    '&:hover': {
      background: '#FFC94D'
    }
  },
  footerText: {
    textAlign: 'center',
    color: '#777',
    fontSize: '0.8rem',
    marginTop: 8
  },

  /* LIMITED SEATS */
  limitedBox: {
    background: 'rgba(255, 99, 71, 0.1)',
    color: '#FF4D4D',
    padding: '6px 12px',
    borderRadius: '20px',
    fontWeight: 'bold',
    fontSize: '0.9rem',
    textAlign: 'center',
    margin: '10px auto',
    border: '1px solid #FF4D4D',
    display: 'inline-block',
    animation: '$pulse 1.5s infinite ease-in-out'
  },

  /* MOBILE SWIPER */
  swiperContainer: {
    marginTop: '2rem',
    padding: '1rem',
    display: 'none',
    [theme.breakpoints.down('md')]: {
      display: 'block'
    },
    '& .swiper-pagination-bullet': {
      background: '#666'
    },
    '& .swiper-pagination-bullet-active': {
      background: '#FDB72E'
    }
  },

  /* SOCIAL PROOF */
  socialProofSection: {
    textAlign: 'center',
    padding: '4rem 1rem',
    background: '#111',
    marginTop: '3rem'
  },
  testimonialCard: {
    background: '#1A1A1A',
    border: '1px solid rgba(253,183,46,0.3)',
    padding: '1.5rem',
    borderRadius: 12,
    minHeight: 160
  },
  star: {
    color: '#FDB72E'
  },

  /* LIVE WINNERS */
  liveWinners: {
    background: '#132B27',
    padding: '1rem',
    marginTop: '2rem',
    overflow: 'hidden',
    borderTop: '1px solid rgba(255,255,255,0.1)',
    borderBottom: '1px solid rgba(255,255,255,0.1)'
  },
  winnerTicker: {
    display: 'flex',
    gap: 32,
    animation: '$scroll 45s linear infinite',
    '&:hover': {
      animationPlayState: 'paused'
    }
  },
  winnerItem: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    whiteSpace: 'nowrap',
    padding: '6px 12px',
    background: 'rgba(255,255,255,0.05)',
    borderRadius: 20
  },
  winnerIcon: {
    color: '#FDB72E'
  },

  /* FAQ */
  faqSection: {
    background: '#111',
    padding: '3rem 1rem',
    marginTop: '3rem'
  },
  faqTitle: {
    textAlign: 'center',
    fontWeight: '700',
    fontSize: '2rem',
    color: '#FDB72E',
    marginBottom: '1.5rem'
  },
  accordion: {
    background: '#1A1A1A !important',
    color: '#fff',
    marginBottom: '0.5rem',
    border: '1px solid rgba(255,255,255,0.1)'
  },

  /* Animations */
  '@keyframes pulse': {
    '0%': { transform: 'scale(1)', opacity: 1 },
    '50%': { transform: 'scale(1.05)', opacity: 0.8 },
    '100%': { transform: 'scale(1)', opacity: 1 }
  },
  '@keyframes scroll': {
    '0%': { transform: 'translateX(0)' },
    '100%': { transform: 'translateX(-50%)' }
  }
}))
