import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, Dialog, Box } from '@mui/material'
import { toast } from 'react-hot-toast'
import { PaymentQuery } from '../../../../reactQuery'
import { useDepositInitStore, usePaymentProcessStore, usePortalStore } from '../../../../store/store'
import useStyles from '../Accounts.styles'
import gcCoin from '../../../../components/ui-kit/icons/utils/card-coin2.webp'
import scCoin from '../../../../components/ui-kit/icons/utils/usd-cash.webp'
import limitReached from '../../../../components/ui-kit/icons/svg/limit-reached.svg'
import { limitType } from '../constants'
import { usePaymentStore } from '../../../../store/usePaymentStore'
import { useSubscriptionStore } from '../../../../store/useSubscriptionStore'
import subscriptionQuery from '../../../../reactQuery/subscriptionQuery'

const DepositInprogressModal = ({
  inprogressModalOpen,
  setInprogressModalOpen,
  packageDetails,
  isAppleDevice,
  successToggler,
  errorToggler,
  // setDepositInitiated,
  sessionId,
  isSubscription
}) => {
  const classes = useStyles()
  const setCancelDeposit = usePaymentProcessStore((state) => state.setCancelDeposit)
  const initDeposit = PaymentQuery.initPayMutation({ successToggler, errorToggler })
  const initSubscriptionDeposit = subscriptionQuery.useSubscriptionMutation({ successToggler, errorToggler })
  const portalStore = usePortalStore((state) => state)
  const transactionDetails = useDepositInitStore((state) => state.transactionDetails)
  // SETTERS - PAYMENT STORE
  const { setPaymentData } = usePaymentStore()

  const { setPaymentData: setSubscriptionPaymentData } = useSubscriptionStore()

  const cancelDeposit = PaymentQuery.cancelDepositMutation({
    onSuccess: (res) => {
      setCancelDeposit(false)
      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')

      if (res?.data?.success && transactionDetails?.success === false) {
        if (isSubscription) {
          initSubscriptionDeposit.mutate({
            subscriptionId: packageDetails?.subscriptionId,
            isTrial: packageDetails?.isTrialAllowed
          })
        } else {
          initDeposit.mutate({
            packageId: packageDetails?.packageId,
            subPackageId: packageDetails?.subPackageId,
            isAppleDevice: isAppleDevice,
            sessionKey: sessionId,
            rtyuioo: sessionId === ' ' ? true : false
          })
        }
      }
    },
    onError: (error) => {
      console.log('Internal Server Error', error)
      setCancelDeposit(false)
    }
  })

  const handleButtonClick = async (type) => {
    if (isSubscription) {
      setSubscriptionPaymentData('depositInitiated', false)
    } else {
      setPaymentData('depositInitiated', false)
    }
    setInprogressModalOpen(false)
    if (type === 'yes') {
      try {
        await cancelDeposit.mutate({ transactionId: transactionDetails?.data?.transactionId })
      } catch (error) {
        console.error('Cancel deposit failed:', error)
      }
    }
    if (type === 'no') {
      toast.error(
        'Transaction in progress. If any amount is deducted, it will be refunded within 5 working days. Need help? Contact <EMAIL>.',
        { duration: 5000 }
      )
      setInprogressModalOpen(false)
      portalStore.closePortal()
    }
  }

  const handleClose = () => {
    setInprogressModalOpen(false)
    portalStore.closePortal()
  }

  return (
    <Dialog open={inprogressModalOpen}>
      <Box className={`${classes.couponBox} ${classes.inProgress}`} sx={{ padding: '15px', background: '#1B1B1B' }}>
        {!transactionDetails?.limitCheck?.limitReached ? (
          <>
            <Typography className='deposit-failed'>Previous Deposit In Progress</Typography>
            <Typography className='payment-head-text'>
              {/* {transactionDetails?.message} */}
              It looks like your previous deposit is still being processed. Would you like to{' '}
              <span style={{ color: '#fdb72e' }}>wait</span> for it to complete or{' '}
              <span style={{ color: '#fdb72e' }}>cancel</span> it and start a new one?
            </Typography>
          </>
        ) : (
          <>
            <img className='limit-reached' src={limitReached} alt='limit-reached' />
            <Typography className='deposit-failed'>
              You’ve reached your {limitType[transactionDetails?.limitCheck?.limitType]} purchase limit of $
              {transactionDetails?.limitCheck?.limitAmount}.
            </Typography>
            <Typography className='payment-head-text limit-text'>{transactionDetails?.limitCheck?.message}</Typography>
          </>
        )}

        <Grid className='payment-coupon-main'>
          <Grid className='payment-btn-container'>
            {!transactionDetails?.limitCheck?.limitReached ? (
              <>
                <Grid className='failed-transactions-details'>
                  <Grid className='inprogress-head'>
                    <Typography>In Progress Purchase Summary</Typography>
                  </Grid>
                  <Grid className='detail-box'>
                    <Typography className='payment-coupon-text'>Amount:</Typography>
                    <span>$ {transactionDetails?.data?.amount}</span>
                  </Grid>
                  <Grid className='detail-box'>
                    <Typography className='payment-coupon-text'>SC Coin: </Typography>
                    <span>
                      <img src={scCoin} alt='sc-coin' />
                      {transactionDetails?.data?.scCoin} SC{' '}
                      <span style={{ color: '#00A30B', fontWeight: '600' }}>
                        {transactionDetails?.data?.bonusSc > 0 && ` + ${transactionDetails?.data?.bonusSc} Bonus`}
                      </span>
                    </span>
                  </Grid>
                  <Grid className='detail-box'>
                    <Typography className='payment-coupon-text'>GC Coin: </Typography>
                    <span>
                      <img src={gcCoin} alt='sc-coin' />
                      {transactionDetails?.data?.gcCoin} GC{' '}
                      <span style={{ color: '#00A30B', fontWeight: '600' }}>
                        {transactionDetails?.data?.bonusGc > 0 && ` + ${transactionDetails?.data?.bonusGc} Bonus`}
                      </span>
                    </span>
                  </Grid>
                  <Grid className='detail-box'>
                    <Typography className='payment-coupon-text'>
                      Transaction Id: <span>{transactionDetails?.data?.transactionId}</span>
                    </Typography>
                  </Grid>
                </Grid>
                <Grid className='payment-button-container'>
                  <Button className='yes-button btn btn-primary' onClick={() => handleButtonClick('yes')}>
                    Start New
                  </Button>
                  <Button className='no-button btn btn-primary' onClick={() => handleButtonClick('no')}>
                    Wait
                  </Button>
                </Grid>
              </>
            ) : (
              <>
                <Grid className='payment-img-container'>{/* add limit reaced img here  */}</Grid>
                <Grid className='payment-button-container'>
                  <Button className='no-button btn btn-primary' onClick={handleClose}>
                    Back
                  </Button>
                </Grid>
              </>
            )}
          </Grid>
        </Grid>
      </Box>
    </Dialog>
  )
}

export default DepositInprogressModal
