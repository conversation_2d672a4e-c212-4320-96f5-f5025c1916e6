import { <PERSON>, Button, Grid, <PERSON>b, <PERSON><PERSON>, <PERSON><PERSON>ield, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import useStyles from './Settings.styles'
import EmailVerified from '../../components/ui-kit/icons/svg/email-verified.svg'
import SettingSecurity from '../../components/ui-kit/icons/svg/setting-security.svg'
import SettingsPreferences from '../../components/ui-kit/icons/svg/settings-preferences.svg'
import Settingskyc from '../../components/ui-kit/icons/svg/settings-kyc.svg'
import SettingsUser from '../../components/ui-kit/icons/svg/settings-user.svg'
import PaymentSettings from '../../components/ui-kit/icons/svg/payment_transaction_icon.svg'
import Security from './components/SecurityTab/Security'
import MarketingSettingsForm from './components/Prefernces/Marketing'
import { useLocation } from 'react-router-dom'
import { useGetProfileMutation } from '../../reactQuery'
import KycVerification from './components/SecurityTab/KycVerification'
import PhoneVerification from './components/General/PhoneVerification'
import PreferredPayments from './components/PrefferedPayments/PrefferedPayments'
import Subscriptions from './components/Subscriptions/Subscriptions'
import { SubscriptionIcon } from '../../components/ui-kit/icons/svg'

const Settings = () => {
  const classes = useStyles()
  const location = useLocation()
  const [value, setValue] = React.useState(0)
  const [user, serUser] = useState('')

  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  const { mutate: getProfileMutation, isLoading } = useGetProfileMutation({
    onSuccess: (res) => {
      serUser(res?.data?.data)
    },
    onError: (error) => {
      console.log('Error getting profile:', error)
    }
  })

  useEffect(() => {
    if (location.state && location.state.tabIndex !== undefined) {
      setValue(location.state.tabIndex)
    }
  }, [location.state])

  useEffect(() => {
    getProfileMutation()
  }, [])
  return (
    <Grid className={classes.lobbyRight}>
      <Box className='settings-page-wrap'>
        <Grid className={classes.roundedThemeTabs}>
          <Tabs
            value={value}
            onChange={handleChange}
            variant='scrollable'
            scrollButtons='auto' // Enables automatic left and right scroll buttons
            allowScrollButtonsMobile // Ensures scroll buttons are visible on mobile devices
            aria-label='scrollable auto tabs example'
          >
            <Tab label='General' icon={<img src={SettingsUser} alt='General' />} />
            <Tab label='Security' icon={<img src={SettingSecurity} alt='Security' />} />
            <Tab label='Preferences' icon={<img src={SettingsPreferences} alt='Preferences' />} />
            <Tab label='KYC' icon={<img src={Settingskyc} alt='Kyc' />} />
            <Tab label='Payment' icon={<img src={PaymentSettings} alt='Preffered Payments' />} />
            <Tab label='Subscriptions' icon={<img src={SubscriptionIcon} alt='Subscriptions' />} />
          </Tabs>
        </Grid>
        <Grid className='setting-tab-details'>
          {value === 0 && (
            <>
              <Box className='genral-tab'>
                <Grid className='setting-card-header'>
                  <Typography variant='h4'>
                    Email
                    {user?.email && <img src={EmailVerified} alt='Verified' />}
                  </Typography>
                </Grid>
                <Grid className='setting-card-details'>
                  <Grid container spacing={1}>
                    <Grid item xs={12} sm={5} lg={4}>
                      <TextField
                        id='outlined-basic'
                        // label="Enter Email"
                        value={user?.email}
                        variant='outlined'
                        InputProps={{
                          readOnly: true,
                          style: { cursor: 'default' }
                        }}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                <Grid className='setting-card-footer'>
                  {/* <Button type="button" className="text-btn">Resend Email</Button> */}
                  <Button type='button' className='btn btn-primary' disabled>
                    Save
                  </Button>
                </Grid>
              </Box>

              <PhoneVerification />
            </>
          )}
          {value === 1 && <Security user={user} />}
          {value === 2 && (
            <>
              <MarketingSettingsForm />
            </>
          )}
          {value === 3 && <KycVerification user={user} />}
          {value === 4 && <PreferredPayments />}
          {value === 5 && <Subscriptions />}
        </Grid>
      </Box>
    </Grid>
  )
}

export default Settings
