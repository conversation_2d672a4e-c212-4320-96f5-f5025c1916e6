import { useQuery, useMutation } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import { cancelSubscription, getPlayerSubscriptionData, getSubscription, getSubscriptionsData, initiateSubscription, processSubscriptionPayment } from '../utils/apiCalls'

const getSubscriptionMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.GET_SUBSCRIPTION],
    mutationFn: () => getSubscription(),
    onSuccess,
    onError
  })
}

const getSubscriptionDataQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_SUBSCRIPTIONS_DATA],
    queryFn: () => {
      return getSubscriptionsData(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const useSubscriptionMutation = ({ successToggler, errorToggler, loadingToggler }) => {
  return useMutation({
    mutationKey: ['initiateSubscription'],
    mutationFn: (data) => initiateSubscription(data),
    onSuccess: (data) => {
      successToggler && successToggler(data?.data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
    onSettled: () => {
      loadingToggler && loadingToggler(false)
    }
  })
}

const useProcessSubscriptionMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: ['processSubscriptionPayment'],
    mutationFn: (data) => processSubscriptionPayment(data),
    onSuccess: onSuccess,
    onError: onError
  })
}

const useCancelSubscriptionMutation = ({ onSuccess, onError }) => {
  return useMutation({
    mutationKey: [KeyTypes.CANCEL_SUBSCRIPTION],
    mutationFn: (data) => cancelSubscription(data),
    onSuccess: onSuccess,
    onError: onError
  })
}

const getPlayerSubscriptionDataQuery = ({ successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_PLAYER_SUBSCRIPTION_DATA],
    queryFn: () => {
      return getPlayerSubscriptionData()
    },
    select: (data) => {
      return data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const subscriptionQuery = {
  getSubscriptionMutation,
  getSubscriptionDataQuery,
  useSubscriptionMutation,
  useProcessSubscriptionMutation,
  useCancelSubscriptionMutation,
  getPlayerSubscriptionDataQuery
}
export default subscriptionQuery
