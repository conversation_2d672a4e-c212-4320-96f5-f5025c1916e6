import React, { lazy, Suspense } from 'react'
import GeoBlock from './pages/GeoBlock'
import ComingSoon from './pages/ComingSoon'
import ReferPage from './pages/ReferPage'
import Package from './pages/Package'
import HallOfFame from './pages/HallOfFame'
import GamesPage from './components/SeoLandingPages/GamesPage'
import BlogPage from './components/SeoLandingPages/BlogSection/BlogPage'
// import CommonArticlePage from './components/SeoLandingPages/BlogSection/CommonArticlePage'
import FAQ from './components/SeoLandingPages/FAQ Section/faq'
import ContactUsPage from './components/SeoLandingPages/ContactUs/ContactUsPage'
import AboutUsPage from './components/SeoLandingPages/AboutUs/AboutUsPage'
import LandingWrapper from './LandingWrapper'
// import JackpotCasinoGames from './components/SeoLandingPages/JackpotCasino/JackpotCasinoGames'
// import SocialSlotGames from './components/SeoLandingPages/SlotGames/SocialSlotGames'
// import ScratchCardGames from './components/SeoLandingPages/ScratchCardGame/index'
// import InstantWinCasino from './components/SeoLandingPages/InstantWinCasino/index'
// import LiveDealerCasino from './components/SeoLandingPages/LiveDealerCasino/index'
import DynamicBlog from './components/SeoLandingPages/BlogSection/DynamicBlog'
import ReferralWrapper from './ReferralWrapper'
import VipQuestionWrapper from './VipQuestionWrapper'
import DynamicVipQuestionForm from './pages/Accounts/components/DynamicVipForm'
import ProfileSection from './pages/Accounts/components/ProfileSection'
import ResponsibleGambling from './pages/ResponsibleGambling'
import NotAvailable from './pages/NotAvailable'
import NotFoundPage from './pages/NotFoundPage'
import Jackpot from './pages/Jackpot'
import { PlayerRoutes } from './routes'
import Reward from './pages/Reward/Reward'
import Loader from './components/Loader'
import SeoGamePageWrapper from './SeoGamePageWrapper'
import Subscriptions from './pages/Subscription'

// Lazy imports
const Affiliates = lazy(() => import('./pages/Affiliates'))
const Lobby = lazy(() => import('./pages/Lobby/Lobby'))
const PromotionsPage = lazy(() => import('./pages/PromotionsPage'))
const BetHistorySection = lazy(() => import('./pages/Accounts/components/BetHistorySection'))
const ReferFriend = lazy(() => import('./pages/ReferFriend'))
const TournamentsPage = lazy(() => import('./pages/TournamentsPage/index'))
const Tier = lazy(() => import('./pages/Tier/index'))
const TournamentDetail = lazy(() => import('./pages/TournamentsPage/TournamentDetail'))
const Accounts = lazy(() => import('./pages/Accounts'))
const Cms = lazy(() => import('./pages/Cms/Cms'))
const GamePlay = lazy(() => import('./pages/GamePlay/GamePlay'))
const Landing = lazy(() => import('./pages/Landing/index'))
const Settings = lazy(() => import('./pages/Settings/index'))
const RedeemSuccessPopup = lazy(() => import('./components/StepperForm/RedeemSuccessPopup'))
const RedeemFailedPopup = lazy(() => import('./components/StepperForm/RedeemFailedPopup'))

const appRoutes = [
  {
    path: PlayerRoutes.DefaultRoute,
    element: <LandingWrapper /> // Assuming LandingWrapper is critical and not lazy-loaded
  },
  {
    path: PlayerRoutes.Account,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <Accounts />
      </Suspense>
    ),
    level: [
      {
        path: '',
        private: true,
        element: <ProfileSection /> // Assuming ProfileSection is small or always needed with Accounts
      },
      {
        path: PlayerRoutes.UserResponsibleGaming,
        private: true,
        element: <ResponsibleGambling /> // Assuming ResponsibleGambling is small or always needed with Accounts
      },
      {
        path: PlayerRoutes.VipPlayerInterests,
        private: true,
        element: (
          <VipQuestionWrapper>
            <Suspense fallback={<Loader />}>
              <DynamicVipQuestionForm />
            </Suspense>
          </VipQuestionWrapper>
        )
      }
    ]
  },
  {
    path: PlayerRoutes.Landing,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.RedeemSuccess,
    element: (
      <Suspense fallback={<Loader />}>
        <RedeemSuccessPopup />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.RedeemFailed,
    element: (
      <Suspense fallback={<Loader />}>
        <RedeemFailedPopup />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Lobby,
    element: (
      <Suspense fallback={<Loader />}>
        <Lobby />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.VerifyEmail,
    element: (
      <Suspense fallback={<Loader />}>
        <Lobby />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Referral,
    element: (
      <ReferralWrapper>
        <Suspense fallback={<Loader />}>
          <Landing />
        </Suspense>
      </ReferralWrapper>
    )
  },
  {
    path: PlayerRoutes.ForgotPassword,
    element: (
      <Suspense fallback={<Loader />}>
        <Lobby />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Store,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <Package />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.DepositStatus,
    element: (
      <Suspense fallback={<Loader />}>
        <Package />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.GamePlay,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <GamePlay />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Reward,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <Reward />
      </Suspense>
    )
  },
  { path: PlayerRoutes.Geoblock, element: <GeoBlock /> }, // Keep if small or critical
  { path: PlayerRoutes.ComingSoon, element: <ComingSoon /> }, // Keep if small or critical
  {
    path: PlayerRoutes.Cms,
    element: (
      <Suspense fallback={<Loader />}>
        <Cms />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.ResponsibleGambling,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <ResponsibleGambling />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.PromotionsPage,
    element: (
      <Suspense fallback={<Loader />}>
        <PromotionsPage />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Bets,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <BetHistorySection />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Affiliate,
    element: (
      <Suspense fallback={<Loader />}>
        <Affiliates />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.ReferFriend,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <ReferFriend />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.TournamentsPage,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <TournamentsPage />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.TournamentsDetail,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <TournamentDetail />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.GamePlayTournament,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <GamePlay />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Tier,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <Tier />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Settings,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <Settings />
      </Suspense>
    )
  },
  { path: PlayerRoutes.ReferPage, element: <ReferPage /> }, // Keep if small or critical
  { path: PlayerRoutes.HallOfFame, element: <HallOfFame /> }, // Keep if small or critical
  {
    path: PlayerRoutes.RealMoneyCasinoGames,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.RealMoneyCasinoSlots,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.LiveDealerCasino,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.InstantWinGame,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.CasinoTableGame,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.OnlineCasinosBonus,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.CasinoCalifornio,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.CasinoFlorida,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.CasinoTexas,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  { path: PlayerRoutes.NotAvailable, element: <NotAvailable /> }, // Keep if small or critical
  { path: PlayerRoutes.seoDynamicGamePageUrl, element: <SeoGamePageWrapper />}, // More specific route must come first
  { path: PlayerRoutes.Games, element: <GamesPage /> }, // Keep if small or critical
  { path: PlayerRoutes.Blog, element: <BlogPage /> }, // Keep if small or critical
  {
    path: PlayerRoutes.PaymentStatus,
    element: (
      <Suspense fallback={<Loader />}>
        <Package />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.TrustlyRedeem,
    element: (
      <Suspense fallback={<Loader />}>
        <RedeemSuccessPopup />
      </Suspense>
    )
  },
  { path: PlayerRoutes.FAQ, element: <FAQ /> }, // Keep if small or critical
  { path: PlayerRoutes.ContactUs, element: <ContactUsPage /> }, // Keep if small or critical
  { path: PlayerRoutes.AboutUs, element: <AboutUsPage /> }, // Keep if small or critical
  {
    path: PlayerRoutes.Jackpot,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <Jackpot />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Subscriptions,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <Subscriptions />
      </Suspense>
    )
  },
  { path: PlayerRoutes.DynamicBlog, element: <DynamicBlog /> }, // Keep if small or critical
  { path: '*', element: <NotFoundPage /> } // Keep if small or critical
]

export default appRoutes
