import { makeStyles } from '@mui/styles'

import streakBg from '../../components/ui-kit/icons/png/bonus-box1.png'

export default makeStyles((theme) => ({
  bonusModalWrap: {
    // minWidth: theme.spacing(36.25),
    background: 'transparent',
    position: 'relative',

    '& .MuiModal-backdrop': {
      backdropFilter: 'inherit',
      background: '#000000CC'
    },
    '& .MuiDialog-paperScrollPaper ': {
      backgroundColor: 'transparent !important',
      // display: 'flow-root',
      position: 'relative',
      overflowY: 'visible',
      borderRadius: theme.spacing(1.125),
      '&>::before': {
        content: "''",
        position: 'absolute',
        top: '0',
        left: '0',
        right: '0',
        bottom: '0',
        borderRadius: theme.spacing(1.125),
        border: '2px solid transparent',
        background: 'linear-gradient(180deg, #FDB72E 0%, #8B451C 100%) border-box',
        WebkitMask: ' linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)',
        WebkitMaskComposite: 'destination-out',
        maskComposite: 'exclude'
      }
    },
    '& .next-timer': {
      position: 'absolute',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      top: '-5rem',
      [theme.breakpoints.down('md')]: {
        top: '-3.5rem'
      },
      '& button': {
        display: 'flex',
        justifyContent: 'center',
        gap: '0.25rem',
        minWidth: '12.5rem',
        padding: '0.5rem 0.25rem',
        alignItems: 'center',
        background: 'linear-gradient(180deg, #FDB72E 0%, #BA5C25 100%)',
        borderRadius: '4rem',
        border: 'none',
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          minWidth: '7.5rem'
        },
        '& p': {
          fontSize: '1.5rem',
          fontWeight: '700',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.75rem'
          }
        },
        '& h5': {
          fontSize: '1.5rem',
          fontWeight: '600',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.75rem'
          }
        }
      }
    },
    '& .bonus-ribbon': {
      position: 'absolute',
      top: '43px',
      left: '50%',
      padding: '1.25rem 3.125rem',
      transform: 'translate(-50%, -50%)',
      [theme.breakpoints.down('md')]: {
        padding: '1.25rem 1.125rem',
        top: '28px'
      },
      '& img': {
        [theme.breakpoints.down('md')]: {
          width: '13.375rem'
        }
      },
      '& h3': {
        fontSize: '2.5rem',
        position: 'absolute',
        left: '50%',
        top: '50%',
        lineHeight: '0.9',
        textAlign: 'center',
        transform: 'translate(-50%, -50%)',
        fontWeight: '700',
        background: 'linear-gradient(180deg, #FFEA94 16.67%, #FFA538 80.43%)',
        WebkitBackgroundClip: 'text',
        textShadow: '0px 4.09px 6.13px 0px #A3A1A199',
        WebkitTextFillColor: 'transparent',
        [theme.breakpoints.down('md')]: {
          fontSize: '1.25rem'
        }
      }
    },
    '& .bonus-streak-modal': {
      minWidth: '36.25rem',
      background: 'transparent',
      marginTop: '50px',
      [theme.breakpoints.down('md')]: {
        minWidth: 'auto'
      },
      [theme.breakpoints.down('sm')]: {
        marginTop: '50px'
      },
      '& .MuiDialogContent-root ': {
        padding: '1.625rem 3.125rem 1.625rem',
        position: 'relative',
        [theme.breakpoints.down('md')]: {
          padding: '3.75rem 0.5rem 0.75rem'
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '600',
          textAlign: 'center',
          color: theme.colors.textWhite,
          lineHeight: '1',
          [theme.breakpoints.down('md')]: {
            fontSize: '0.75rem'
          }
        },
        '& .progress-main-wrap': {
          width: '100%',
          padding: '1.5rem 1rem 1.75rem 1rem',
          [theme.breakpoints.down('md')]: {
            padding: '1rem 1rem 2.25rem 1rem !important'
          },
          [theme.breakpoints.down('sm')]: {
            padding: '0.5rem 0.75rem 1.25rem 0.75rem !important'
          }
        },
        '& .progress-bar': {
          padding: '0 10px',
          width: '100%',
          position: 'absolute',
          bottom: '2px',
          left: '0'
        },
        '& .progress-wrap': {
          position: 'relative',
          '& .MuiStack-root': {
            padding: '4px 2px',
            borderRadius: '10px',
            width: '100%',
            background: '#BA5C25',
            boxShadow: '0px 7.89px 6.32px 0px #00000066 inset',
            '& .MuiLinearProgress-root': {
              height: '7px',
              borderRadius: '10px',
              background: 'transparent',
              '& .MuiLinearProgress-bar ': {
                borderRadius: '10px',
                background: '#13D12E',
                boxShadow: '0px 0px 4.74px 0px #13D12E'
              }
            }
          },
          '& .progress-content': {
            display: 'flex',
            justifyContent: 'space-between',

            '& .content-box': {
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              justifyContent: 'end',
              alignItems: 'center',
              '& .label-dot': {
                height: '20px',
                width: '20px',
                borderRadius: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '1.5px solid #f3f3f3',
                background: '#FDB72E',
                fontSize: '1rem',
                fontWeight: '600'
              },
              '& img': {
                width: '35px'
              }
            }
          }
        },
        '& .streak-box': {
          borderRadius: '19px',
          padding: '8px 14px',
          backgroundImage: `url(${streakBg})`,
          backgroundPosition: 'center',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          display: 'flex',
          justifyContent: 'space-between',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '0.5rem',
          height: '100%',
          minWidth: '6.5rem',
          cursor: 'pointer',
          position: 'relative',
          boxShadow: '6.3px 6.3px 5.04px 0px #00000040 inset,0px 5.04px 5.04px 0px #00000040',
          [theme.breakpoints.down('md')]: {
            minWidth: 'auto',
            // padding: '4px'
            gap: '0.25rem'
          },
          '& .claimed-box': {
            height: '100%',
            width: '100%',
            position: 'absolute',
            display: 'flex',
            inset: '0',
            borderRadius: '19px',
            top: '0',
            justifyContent: 'center',
            alignItems: 'center',
            background: '#00000099',
            '& .claimed-animation': {
              width: '5rem',
              [theme.breakpoints.down('md')]: {
                width: '4.5rem'
              }
            },
            '& .popper-animation': {
              position: 'absolute',
              height: '100%',
              width: '100%',
              top: '0',
              left: '0'
            }
          },
          '& p': {
            fontSize: '13px',
            fontWeight: '600',
            color: theme.colors.textBlack,
            whiteSpace: 'nowrap'
            // [theme.breakpoints.down('md')]: {
            //   fontSize: '12px'
            // }
          },
          '&.day-1': {
            backgroundColor: '#FFEA94'
          },
          '&.day-2': {
            backgroundColor: '#77AEE8'
          },
          '&.day-3': {
            backgroundColor: '#E87AC6'
          },
          '&.day-4': {
            backgroundColor: '#E99566'
          },
          '&.day-5': {
            backgroundColor: '#A773EB'
          },
          '&.day-6': {
            backgroundColor: '#B6DA76'
          },
          '&.day-7': {
            backgroundColor: '#F7D575',
            justifyContent: 'space-around',
            '& .reward-wrap': {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
              '& .MuiTypography-body1': {
                fontWeight: '700',
                fontSize: '1rem'
              },
              '& img': {
                width: '3.5rem'
              }
            }
          },
          '& img': {
            width: '3.375rem',
            filter: 'drop-shadow(0px 5.04px 5.04px #00000066)',
            [theme.breakpoints.down('sm')]: {
              width: '2.75rem'
            }
          }
        },
        '& .claim-btn': {
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          marginTop: '20px',
          [theme.breakpoints.down('sm')]: {
            marginTop: '12px'
          },
          '& button': {
            minWidth: '15.625rem',
            border: '1px solid #605F5E',
            background: 'transparent',
            minHeight: '3rem',
            cursor: 'pointer',
            fontSize: '1.125rem',
            '&:hover': {
              border: '1px solid transparent',
              background: theme.colors.YellowishOrange,
              color: theme.colors.textBlack
            },
            [theme.breakpoints.down('md')]: {
              minWidth: '10rem',
              fontSize: '0.875rem',
              minHeight: '2.125rem',
              maxHeight: '2rem',
              padding: '0.35rem'
            }
          }
        }
      }
    },

    '& .modal-close': {
      position: 'absolute',
      right: theme.spacing(1),
      top: theme.spacing(1),
      height: theme.spacing(1.375),
      width: theme.spacing(1.375),
      borderRadius: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 2,
      '& .MuiButtonBase-root': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '0',
        margin: '0',
        position: 'relative',
        '& svg': {
          height: theme.spacing(1.5),
          width: theme.spacing(1.5),
          transform: 'translate(-50%, -50%)',
          top: '50%',
          left: '50%',
          position: 'absolute',
          color: theme.colors.textWhite
        }
      }
    }
    // '& .bonus-modal-img-wrap': {
    //   height: theme.spacing(14.5),
    //   width: theme.spacing(14.5),
    //   borderRadius: theme.spacing(1.25),
    //   marginTOp: theme.spacing(1.875),
    //   // overflow: "hidden",
    //   margin: '0 auto',
    //   position: 'relative',
    //   '& > img': {
    //     height: '100%',
    //     width: '100%',
    //     objectFit: 'cover'
    //   },
    //   '& .modal-brand-logo': {
    //     position: 'absolute',
    //     top: theme.spacing(0.4),
    //     left: '50%',
    //     zIndex: '2',
    //     display: 'none',
    //     transform: 'translate(-50%, -50%)',
    //     '& img': {
    //       width: theme.spacing(3)
    //     }
    //   }
    // },
    // '& .bonus-modal-details': {
    //   textAlign: 'center',
    //   '& .bonus-coins-wrap': {
    //     display: 'flex',
    //     alignItems: 'center',
    //     justifyContent: 'center',
    //     gap: theme.spacing(0.625),
    //     margin: theme.spacing(0.625, 0),
    //     '& > .MuiGrid-root': {
    //       display: 'flex',
    //       alignItems: 'center',
    //       '& img': {
    //         marginRight: theme.spacing(0.313)
    //       },
    //       '& .MuiTypography-h4': {
    //         color: theme.colors.primaryYellowBg,
    //         fontWeight: theme.typography.fontWeightSemiBold,
    //         fontSize: theme.spacing(1.8)
    //       }
    //     }
    //   },
    //   '& .MuiTypography-body1': {
    //     color: theme.colors.textWhite,
    //     textAlign: 'center',
    //     textTransform: 'capitalize',
    //     marginBottom: theme.spacing(1)
    //   },
    //   '& .btn-primary': {
    //     background: theme.colors.primaryYellowBg,
    //     color: theme.colors.textBlack,
    //     borderRadius: theme.spacing(1.875),
    //     fontWeight: theme.typography.fontWeightBoldBlack,
    //     width: '100%'
    //   },
    //   '& .bonus-terms': {
    //     textAlign: 'center',
    //     '& .MuiTypography-body1': {
    //       fontSize: theme.spacing(0.625),
    //       textAlign: 'center',
    //       maxWidth: theme.spacing(9.375),
    //       margin: '0.625rem auto 0'
    //     }
    //   }
    // }
  }
}))
